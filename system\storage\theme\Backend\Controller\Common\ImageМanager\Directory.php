<?php

namespace Theme25\Backend\Controller\Common\ImageManager;

class Directory extends \Theme25\ControllerSubMethods {

    /**
     * Зарежда съдържанието на директория
     * 
     * @return array Резултат с данни за директорията
     */
    public function loadContents() {
        $this->loadLanguage('common/filemanager');
        
        // Валидация на директория и права на достъп
        $validationController = $this->setBackendSubController('Common/ImageManager/Validation', $this->_controller);
        $validation = $validationController->validateDirectoryAccess();

        $directory = $this->getCurrentDirectory();

        // Debug информация
        F()->log->developer([
            'directory_param' => $directory ?? 'not_set',
            'validation_result' => $validation,
            'catalog_path' => ThemeData()->getImageCatalogPath()
        ], __FILE__, __LINE__);

        F()->log->developer($directory, __FILE__, __LINE__);

        if (!$validation['valid']) {
            return [
                'success' => false,
                'error' => $validation['error'],
                'debug' => [
                    'directory_param' => $directory ?? 'not_set',
                    'validation' => $validation
                ]
            ];
        }
        
        $directory = $validation['directory'];

        // Зареждане на съдържанието
        return $this->loadDirectoryContents($directory);
    }
    
    /**
     * Зарежда съдържанието на конкретна директория
     *
     * @param string $directory Път до директорията
     * @return array Резултат с данни за директорията
     */
    public function loadDirectoryContents($directory) {
        $this->loadModelAs('tool/image', 'imageModel');

        // Получаване на параметри за pagination
        $page = (int)($this->requestGet('page') ?? 1);

        // Изчисляваме динамично броя изображения въз основа на размерите на модала
        $limit = $this->calculateOptimalImageLimit();
        $offset = ($page - 1) * $limit;

        // Получаване на папки и файлове
        $allItems = $this->getDirectoryItems($directory);

        // Сортиране - папки първо, след това файлове
        $sortedItems = $this->sortItems($allItems);

        // Разделяме папки и файлове за pagination
        $directories = array_filter($sortedItems, function($item) {
            return $item['type'] === 'directory';
        });

        $images = array_filter($sortedItems, function($item) {
            return $item['type'] === 'image';
        });

        // Папките се показват само на първата страница
        // Файловете се показват с pagination
        $totalImages = count($images);
        $paginatedImages = array_slice($images, $offset, $limit);

        // За първата страница комбинираме папки с изображения
        // За следващите страници показваме само изображения
        if ($page === 1) {
            $items = array_merge($directories, $paginatedImages);
        } else {
            $items = $paginatedImages;
        }

        // Създаване на breadcrumb навигация
        $breadcrumb = $this->createBreadcrumb();

        // Получаване на родителската директория
        $parentDirectory = $this->getParentDirectory();

        return [
            'success' => true,
            'items' => $items,
            'breadcrumb' => $breadcrumb,
            'current_directory' => $this->getCurrentDirectory(),
            'parent_directory' => $parentDirectory,
            'base_url' => ThemeData()->getImageServerUrl(),
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total_images' => $totalImages,
                'has_more' => ($offset + $limit) < $totalImages,
                'loaded_images' => count($paginatedImages)
            ]
        ];
    }
    
    /**
     * Получава всички елементи в директорията (папки и файлове)
     * 
     * @param string $directory Път до директорията
     * @return array Масив с елементи
     */
    private function getDirectoryItems($directory) {
        $items = [];
        
        // Получаване на папки
        $directories = glob($directory . '/*', GLOB_ONLYDIR);
        if ($directories) {
            foreach ($directories as $dir) {
                $items[] = $this->createDirectoryItem($dir);
            }
        }
        
        // Получаване на изображения
        $files = glob($directory . '/*.{jpg,jpeg,png,gif,webp,JPG,JPEG,PNG,GIF,WEBP}', GLOB_BRACE);
        if ($files) {
            foreach ($files as $file) {
                $items[] = $this->createImageItem($file);
            }
        }
        
        return $items;
    }
    
    /**
     * Сортира елементите - папки първо, след това файлове
     * 
     * @param array $items Масив с елементи
     * @return array Сортиран масив
     */
    private function sortItems($items) {
        usort($items, function($a, $b) {
            if ($a['type'] != $b['type']) {
                return $a['type'] == 'directory' ? -1 : 1;
            }
            return strcasecmp($a['name'], $b['name']);
        });
        
        return $items;
    }
    
    /**
     * Създава елемент за папка
     * 
     * @param string $dirPath Път до папката
     * @return array Данни за папката
     */
    public function createDirectoryItem($dirPath) {
        $name = basename($dirPath);
        $relativePath = str_replace(ThemeData()->getImageCatalogPath(), '', $dirPath);
        
        return [
            'type' => 'directory',
            'name' => $name,
            'path' => $relativePath,
            'thumb' => '',
            'size' => 0,
            'date_modified' => date('Y-m-d H:i:s', filemtime($dirPath))
        ];
    }
    
    /**
     * Създава елемент за изображение
     *
     * @param string $filePath Път до файла
     * @return array Данни за изображението
     */
    public function createImageItem($filePath) {
        $name = basename($filePath);
        $relativePath = str_replace(ThemeData()->getImageServerPath(), '', $filePath);

        $relativePath = str_replace('//', '/', $relativePath);

        // Проверяваме дали миниатюрата вече съществува в cache
        $cachedThumb = $this->getCachedThumbnailPath($relativePath);

        // Анализ на файловия формат
        $formatAnalysis = $this->analyzeImageFormat($filePath);

        return [
            'type' => 'image',
            'name' => $name,
            'path' => $relativePath,
            'thumb' => $cachedThumb, // Директен път ако съществува
            'thumb_url' => $cachedThumb ? '' : $this->generateThumbnailUrl($relativePath), // Празен стринг ако има cache, URL ако няма
            'has_cache' => !empty($cachedThumb),
            'size' => filesize($filePath),
            'date_modified' => date('Y-m-d H:i:s', filemtime($filePath)),
            'format_analysis' => $formatAnalysis
        ];
    }

    /**
     * Генерира URL за lazy loading на thumbnail
     *
     * @param string $relativePath Относителен път до изображението
     * @return string URL за thumbnail
     */
    public function generateThumbnailUrl($relativePath) {
        $userToken = $this->getSession('user_token') ?? '';
        return 'index.php?route=common/imagemanager/thumbnail&user_token=' . $userToken . '&image=' . urlencode($relativePath) . '&width=150&height=150';
    }

    public function getCachedThumbnailPath($relativePath) {
        // Зареждаме image модела за проверка на cache
        $this->loadModelAs('tool/image', 'imageModel');

        $relativePath = ltrim($relativePath, '/');
        $relativePath = str_replace('//', '/', $relativePath);

        // Използваме същата логика като в image модела
        $extension = pathinfo($relativePath, PATHINFO_EXTENSION);

        // Според image модела: cache/filename-150x150.extension
        $image_new = 'cache/' . utf8_substr($relativePath, 0, utf8_strrpos($relativePath, '.')) . '-150x150.' . $extension;

        // Проверяваме дали cache файлът съществува
        $fullCachePath = ThemeData()->getImageServerPath() . $image_new;

        // Debug информация за cache
        // F()->log->developer([
        //     'cache_debug' => [
        //         'relative_path' => $relativePath,
        //         'image_new' => $image_new,
        //         'full_cache_path' => $fullCachePath,
        //         'file_exists' => json_encode(file_exists($fullCachePath)),
        //         'server_path' => ThemeData()->getImageServerPath(),
        //         'server_url' => ThemeData()->getImageServerUrl()
        //     ]
        // ], __FILE__, __LINE__);

        if (file_exists($fullCachePath)) {
            // Връщаме пълен URL към cache файла
            return ThemeData()->getImageServerUrl() . $image_new;
        }

        return '';
    }
    
    /**
     * Създава breadcrumb навигация
     * 
     * @return array Breadcrumb данни
     */
    public function createBreadcrumb() {
        $breadcrumb = [];
        $currentDirectory = $this->getCurrentDirectory();
        
        if ($currentDirectory) {
            $parts = explode('/', trim($currentDirectory, '/'));
            $currentPath = '';
            
            $breadcrumb[] = [
                'name' => 'Начало',
                'path' => ''
            ];
            
            foreach ($parts as $part) {
                $currentPath .= ($currentPath ? '/' : '') . $part;
                $breadcrumb[] = [
                    'name' => $part,
                    'path' => $currentPath
                ];
            }
        } else {
            $breadcrumb[] = [
                'name' => 'Начало',
                'path' => ''
            ];
        }
        
        return $breadcrumb;
    }
    
    /**
     * Получава родителската директория
     *
     * @return string Път до родителската директория
     */
    public function getParentDirectory() {
        $currentDirectory = $this->getCurrentDirectory();

        if (empty($currentDirectory)) {
            // Ако сме в root директорията, няма родителска
            return null;
        }

        $parts = explode('/', trim($currentDirectory, '/'));

        if (count($parts) > 1) {
            // Премахваме последната част за да получим родителската
            array_pop($parts);
            return implode('/', $parts);
        } else {
            // Ако има само една част, родителската е root (празен стринг)
            return '';
        }
    }
    
    /**
     * Навигира към конкретна директория
     * 
     * @param string $targetDirectory Целева директория
     * @return array Резултат с данни за новата директория
     */
    public function navigateToDirectory($targetDirectory = '') {
        // Временно задаваме новата директория в request
        $originalDirectory = $this->requestGet('directory');
        $_GET['directory'] = $targetDirectory;
        
        // Зареждаме съдържанието на новата директория
        $result = $this->loadContents();
        
        // Възстановяваме оригиналната директория
        if ($originalDirectory !== null) {
            $_GET['directory'] = $originalDirectory;
        } else {
            unset($_GET['directory']);
        }
        
        return $result;
    }

    private function getCurrentDirectory() {
        return ltrim(urldecode($this->requestGet('directory', '')), '/');
    }

    /**
     * Търсене на изображения и папки рекурсивно
     *
     * @return array Резултат с намерени елементи
     */
    public function searchItems() {
        $query = $this->requestGet('query', '');
        $directory = $this->getCurrentDirectory();

        if (empty($query)) {
            return [
                'success' => false,
                'error' => 'Няма зададена заявка за търсене'
            ];
        }

        $searchResults = $this->performRecursiveSearch($query, $directory);

        return [
            'success' => true,
            'items' => $searchResults,
            'query' => $query,
            'directory' => $directory
        ];
    }

    /**
     * Извършва рекурсивно търсене в директория и нейните подпапки
     *
     * @param string $query Заявка за търсене
     * @param string $directory Директория за търсене
     * @return array Намерени елементи
     */
    private function performRecursiveSearch($query, $directory) {
        $results = [];
        $query = strtolower($query);

        // Търсене в текущата директория
        $items = $this->getDirectoryItems($directory);

        foreach ($items as $item) {
            if (strpos(strtolower($item['name']), $query) !== false) {
                $results[] = $item;
            }

            // Ако елементът е папка, търсим рекурсивно в нея
            if ($item['type'] === 'directory') {
                $subDirectory = $directory . '/' . $item['name'];
                if (is_dir($subDirectory)) {
                    $subResults = $this->performRecursiveSearch($query, $subDirectory);
                    $results = array_merge($results, $subResults);
                }
            }
        }

        return $results;
    }

    /**
     * Изчислява оптималния брой изображения за зареждане въз основа на размерите на браузера
     *
     * @return int Оптимален брой изображения
     */
    private function calculateOptimalImageLimit() {
        // Получаваме размерите на браузера от JavaScript заявката
        $viewportWidth = (int)($this->requestGet('modal_width') ?? 1920);
        $viewportHeight = (int)($this->requestGet('modal_height') ?? 1080);
        $gridWidth = (int)($this->requestGet('grid_width') ?? 800);

        // Ако няма предадени размери, използваме фиксирания брой
        if ($viewportWidth <= 0 || $viewportHeight <= 0) {
            return 30; // fallback към фиксирания брой
        }

        // Изчисляваме приблизителната височина на модала (70% от viewport)
        $modalHeight = floor($viewportHeight * 0.7);

        // Изчисляваме достъпната височина за grid (отчитаме modal header, breadcrumb, toolbar, footer)
        $availableHeight = $modalHeight - 150; // по-консервативна оценка

        // Приблизителни размери на един grid item (включително gap)
        $itemHeight = 120; // височина на изображение + текст + padding
        $itemWidth = 100;  // ширина на изображение + padding
        $gridGap = 8;      // gap между елементите

        // Изчисляваме колко колони се побират в ширината
        $columnsPerRow = max(1, floor($gridWidth / ($itemWidth + $gridGap)));

        // Изчисляваме колко реда се побират във височината
        $rowsVisible = max(1, floor($availableHeight / ($itemHeight + $gridGap)));

        // Общ брой видими изображения
        $visibleImages = $columnsPerRow * $rowsVisible;

        // Добавяме още 1-2 реда за да се създаде scrollbar и да се активира infinite scroll
        $optimalCount = $visibleImages + ($columnsPerRow * 2);

        // Минимум 12, максимум 60 изображения (увеличени лимити за по-голяма гъвкавост)
        return max(12, min(60, $optimalCount));
    }

    /**
     * Анализира формата на изображението и проверява дали разширението съответства на истинския формат
     *
     * @param string $filePath Път до файла
     * @return array Резултат от анализа
     */
    public function analyzeImageFormat($filePath) {
        $result = [
            'extension_from_name' => '',
            'actual_format' => '',
            'mime_type' => '',
            'format_mismatch' => false,
            'warning_message' => '',
            'suggested_extension' => ''
        ];

        // Получаваме разширението от името на файла
        $pathInfo = pathinfo($filePath);
        $result['extension_from_name'] = strtolower($pathInfo['extension'] ?? '');

        try {
            // Използваме getimagesize за получаване на MIME типа
            $imageInfo = getimagesize($filePath);

            if ($imageInfo !== false) {
                $result['mime_type'] = $imageInfo['mime'];

                // Определяме истинския формат от MIME типа
                switch ($imageInfo['mime']) {
                    case 'image/jpeg':
                    case 'image/pjpeg':
                        $result['actual_format'] = 'jpeg';
                        $result['suggested_extension'] = 'jpg';
                        break;
                    case 'image/png':
                        $result['actual_format'] = 'png';
                        $result['suggested_extension'] = 'png';
                        break;
                    case 'image/gif':
                        $result['actual_format'] = 'gif';
                        $result['suggested_extension'] = 'gif';
                        break;
                    case 'image/webp':
                        $result['actual_format'] = 'webp';
                        $result['suggested_extension'] = 'webp';
                        break;
                    default:
                        $result['actual_format'] = 'unknown';
                        $result['suggested_extension'] = '';
                }

                // Проверяваме дали има несъответствие
                $extensionMatches = $this->checkExtensionMatch($result['extension_from_name'], $result['actual_format']);

                if (!$extensionMatches) {
                    $result['format_mismatch'] = true;
                    $result['warning_message'] = "Файлът е в {$result['actual_format']} формат, но има .{$result['extension_from_name']} разширение";
                }

            } else {
                $result['warning_message'] = 'Не може да се определи форматът на файла';
            }

        } catch (Exception $e) {
            $result['warning_message'] = 'Грешка при анализ на формата: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Проверява дали разширението съответства на формата
     *
     * @param string $extension Разширение от името на файла
     * @param string $actualFormat Истинския формат
     * @return bool
     */
    private function checkExtensionMatch($extension, $actualFormat) {
        $validExtensions = [
            'jpeg' => ['jpg', 'jpeg'],
            'png' => ['png'],
            'gif' => ['gif'],
            'webp' => ['webp']
        ];

        if (!isset($validExtensions[$actualFormat])) {
            return false;
        }

        return in_array($extension, $validExtensions[$actualFormat]);
    }

    /**
     * Създава нова папка
     *
     * @param string $folderName Името на папката
     * @param string $currentDirectory Текущата директория
     * @return array Резултат от операцията
     */
    public function createFolder($folderName, $currentDirectory = '') {
        try {
            // Определяме пълния път до новата папка
            $basePath = ThemeData()->getImageServerPath() . 'catalog/';
            $targetPath = $basePath;

            if (!empty($currentDirectory)) {
                $targetPath .= trim($currentDirectory, '/') . '/';
            }

            $newFolderPath = $targetPath . $folderName;

            // Проверяваме дали папката вече съществува
            if (file_exists($newFolderPath)) {
                return [
                    'success' => false,
                    'error' => 'Папка с това име вече съществува'
                ];
            }

            // Проверяваме дали родителската директория съществува
            if (!file_exists($targetPath)) {
                return [
                    'success' => false,
                    'error' => 'Родителската директория не съществува'
                ];
            }

            // Проверяваме дали имаме права за писане в родителската директория
            if (!is_writable($targetPath)) {
                return [
                    'success' => false,
                    'error' => 'Няма права за писане в тази директория'
                ];
            }

            // Създаваме папката
            if (!mkdir($newFolderPath, 0755, true)) {
                return [
                    'success' => false,
                    'error' => 'Грешка при създаване на папката'
                ];
            }

            // Проверяваме дали папката е създадена успешно
            if (!is_dir($newFolderPath)) {
                return [
                    'success' => false,
                    'error' => 'Папката не беше създадена успешно'
                ];
            }

            return [
                'success' => true,
                'message' => 'Папката беше създадена успешно',
                'folder_name' => $folderName,
                'folder_path' => !empty($currentDirectory) ? $currentDirectory . '/' . $folderName : $folderName
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Грешка при създаване на папката: ' . $e->getMessage()
            ];
        }
    }
}
